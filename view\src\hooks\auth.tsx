import {
	type <PERSON>actN<PERSON>,
	create<PERSON>ontext,
	useContext,
	useEffect,
	useState,
} from "react";

import { api } from "./../service/api";

type signOut = () => void;

interface AuthProviderProps {
	children: ReactNode;
}

interface LoginData {
	username: string;
	password: string;
}

interface AuthContextData {
	signIn: (data: LoginData) => Promise<void>;
	signOut: signOut;
	user: User | null;
}

interface User {
	id: number;
	username: string;
	password: string;
	userType: "staff" | "admin";
	personData: {
		personId: number;
		fullname: string;
		contact: string;
		cpf: string;
		cnpj: string;
		personType: "individual" | "private";
	};
}

export const AuthContext = createContext<AuthContextData | null>(null);

function AuthProvider({ children }: AuthProviderProps) {
	const [user, setUser] = useState<{ user: User | null }>({ user: null });

	async function signIn({ username, password }: LoginData) {
		try {
			const response = await api.post("/users/login", { username, password });

			const user = response.data;

			localStorage.setItem("@managermalhas:user", JSON.stringify(user));

			setUser({ user: user });
		} catch (error: any) {
			if (error.response) {
				alert(error.response.data.error);
			} else {
				alert("Nome de usuário ou senha incorreto(s)!");
			}
		}
	}

	async function signOut() {
		localStorage.removeItem("@managermalhas:user");

		setUser({ user: null });
	}

	useEffect(() => {
		const storedUser = localStorage.getItem("@managermalhas:user");

		if (storedUser) {
			const user: User = JSON.parse(storedUser);
			setUser({ user });
		}
	}, []);

	return (
		<AuthContext.Provider value={{ signIn, signOut, user: user?.user || null }}>
			{children}
		</AuthContext.Provider>
	);
}

function useAuth(): AuthContextData {
	const context = useContext(AuthContext);

	if (!context) {
		throw new Error("useAuth deve ser usado dentro de um AuthProvider");
	}

	return context;
}

export { AuthProvider, useAuth };
