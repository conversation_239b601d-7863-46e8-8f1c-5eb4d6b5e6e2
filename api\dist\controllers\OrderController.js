"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const customerRepository_1 = require("../repositories/customerRepository");
const userRepository_1 = require("../repositories/userRepository");
const orderService_1 = require("../services/orderService");
class OrderController {
    static create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { customer_id, staff_id, deadline, status, garments } = req.body;
                const customer = yield customerRepository_1.customerRepository.findOneBy({ id: customer_id });
                if (!customer) {
                    return res.status(404).json({ message: 'Cliente não encontrado!' });
                }
                const staff = yield userRepository_1.userRepository.findOneBy({ id: staff_id });
                if (!staff) {
                    return res.status(404).json({ message: 'Funcionário não encontrado!' });
                }
                if (!deadline) {
                    return res.status(400).json({ message: 'Prazo não foi definido!' });
                }
                if (!status) {
                    return res.status(400).json({ message: 'Status não foi definido!' });
                }
                if (!Array.isArray(garments) || garments.length === 0) {
                    return res.status(400).json({ error: 'É necessário incluir ao menos 1 modelo no pedido!' });
                }
                const date = new Date();
                const time = new Date().toLocaleTimeString();
                const orderData = {
                    customer,
                    user: staff,
                    date,
                    time,
                    deadline,
                    status,
                    garments
                };
                yield orderService_1.OrderService.createOrder(orderData);
                return res.status(201).json({ message: 'Pedido criado com sucesso!' });
            }
            catch (error) {
                console.error(error);
                return res.status(500).json({ message: 'Internal server error' });
            }
        });
    }
    static getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const orders = yield orderService_1.OrderService.getAllOrders();
                if (!orders || orders.length === 0) {
                    return res.status(404).json({ message: 'Nenhum pedido encontrado!' });
                }
                return res.status(200).json(orders);
            }
            catch (error) {
                console.error('Error fetching orders:', error);
                return res.status(500).json({ error: 'Internal server error' });
            }
        });
    }
    static getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const orderId = parseInt(id, 10);
            if (isNaN(orderId)) {
                return res.status(400).json({ message: 'ID de pedido inválido!' });
            }
            try {
                const order = yield orderService_1.OrderService.getOrderById(orderId);
                if (!order) {
                    return res.status(404).json({ message: 'Pedido não encontrado!' });
                }
                return res.status(200).json(order);
            }
            catch (error) {
                console.error('Error fetching order by ID:', error);
                return res.status(500).json({ error: 'Internal server error' });
            }
        });
    }
    static patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const orderId = parseInt(id, 10);
            const updates = req.body;
            if (isNaN(orderId)) {
                return res.status(400).json({ message: 'ID de pedido inválido!' });
            }
            try {
                const existingOrder = yield orderService_1.OrderService.getOrderById(orderId);
                if (!existingOrder) {
                    return res.status(404).json({ error: 'Pedido não encontrado!' });
                }
                const updated = yield orderService_1.OrderService.updateOrder(orderId, updates);
                if (!updated) {
                    return res.status(500).json({ error: 'Falha ao atualizar pedido!' });
                }
                return res.status(200).json({ message: 'Pedido atualizado com sucesso!' });
            }
            catch (error) {
                console.error('Error updating order:', error);
                return res.status(500).json({ error: 'Internal server error' });
            }
        });
    }
    static delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const orderId = parseInt(id, 10);
            if (isNaN(orderId)) {
                return res.status(400).json({ message: 'ID de pedido inválido!' });
            }
            try {
                const result = yield orderService_1.OrderService.deleteOrder(orderId);
                if (result) {
                    return res.status(200).json({ message: 'Pedido deletado com sucesso!' });
                }
                return res.status(404).json({ error: 'Pedido não encontrado!' });
            }
            catch (error) {
                console.error('Error deleting order:', error);
                return res.status(500).json({ error: 'Internal server error' });
            }
        });
    }
}
exports.default = OrderController;
