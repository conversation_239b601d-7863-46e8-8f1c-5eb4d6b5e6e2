import { Footer } from "../../components/Footer";
import { Header } from "../../components/Header";

import { useNavigate } from "react-router-dom";

import { useEffect } from "react";
import { Link } from "react-router";
import { useAuth } from "../../hooks/auth";
import { api } from "../../service/api";

interface User {
	id: number;
	username: string;
	password: string;
	userType: "staff" | "admin";
	personData: {
		personId: number;
		fullname: string;
		contact: string;
		cpf: string;
		cnpj: string;
		personType: "individual" | "private";
	};
}

export function Home() {
	const { signOut } = useAuth();
	const navigate = useNavigate();

	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	function handleSignOut() {
		signOut();
	}

	useEffect(() => {
		const checkUserExistence = async () => {
			const storedUser = localStorage.getItem("@managermalhas:user");

			if (storedUser) {
				try {
					const user = JSON.parse(storedUser);

					const response = await api.get(`/users/${user.id}`);

					if (!response.data) {
						throw new Error("Usuário não encontrado");
					}
				} catch (error) {
					console.error("Usuário não existe ou erro ao verificar:", error);
					signOut();
					navigate("/login");
				}
			}
		};

		checkUserExistence();
	}, [navigate, signOut]);

	return (
		<div className="flex flex-col min-h-screen">
			<Header pagename="Home" $logout={true} href={"/"} func={handleSignOut} />

			<main className="flex-grow text-center font-bold">
				<h1 className="mt-6 mb-8 text-2xl">Bem-vindo ao sistema de Malhas</h1>

				<div
					id="btn-box"
					className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3 mb-3"
				>
					<Link
						to={"/novo-pedido"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Novo Pedido</p>
					</Link>
					<Link
						to={"/novo-cliente"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Novo Cliente</p>
					</Link>
					<Link
						to={"/novo-modelo"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Cadastrar Modelo</p>
					</Link>
					<Link
						to={"/lista-de-pedidos"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Pedidos</p>
					</Link>
					<Link
						to={"/lista-de-clientes"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Clientes</p>
					</Link>
					<Link
						to={"/lista-de-modelos"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Modelos</p>
					</Link>
					<Link
						to={"/nova-materia-prima"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Nova Matéria-Prima</p>
					</Link>
					<Link
						to={"/estoque"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Controle de Estoque</p>
					</Link>
					{user?.userType === "admin" && (
						<Link
							to={"/area-administrativa"}
							type="button"
							className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
						>
							<p>Área de Administração</p>
						</Link>
					)}
				</div>
			</main>

			<Footer />
		</div>
	);
}
