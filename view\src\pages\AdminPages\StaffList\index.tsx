import { useEffect, useState } from "react";
import { FiPlus } from "react-icons/fi";
import { Link } from "react-router-dom";
import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";
import { api } from "../../../service/api";

import { IoClose } from "react-icons/io5";

interface staffUser {
	user_id: number;
	fullname: string;
	contact: string;
	cpf: string | null;
	cnpj: string | null;
	personType: "individual" | "legal";
	username: string;
	password: string;
	userType: "staff" | "admin";
	comissionRate: number;
}

interface User {
	id: number;
	username: string;
	userType: "staff" | "admin";
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
	staff?: {
		id: number;
		comissionRate: string;
	};
}

export function StaffList() {
	const [staffs, setStaffs] = useState<staffUser[]>([]);
	const [searchTerm, setSearchTerm] = useState<string>("");
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	async function handleDeleteUser(user_id: number) {
		if (user_id === user?.id) {
			setAlertMessage(
				"É impossível apagar esse usuário. Provavelmente esse usuário logado é você...",
			);
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		setAlertMessage("Tem certeza que deseja excluir esse usuário?");
		setAlertType("confirm");
		setAlertCallback(() => async () => {
			try {
				await api.delete(`/users/${user_id}`);
				setStaffs([]);
				setAlertMessage("Usuário apagado com sucesso!");
				setAlertType("success");
				setTimeout(() => setIsAlertModalOpen(false), 1500);
			} catch (error) {
				setAlertMessage("Não foi possível deletar o funcionário.");
				setAlertType("error");
				setTimeout(() => setIsAlertModalOpen(false), 1500);
			}
		});
		setIsAlertModalOpen(true);
	}

	useEffect(() => {
		async function getStaff() {
			try {
				const response = await api.get("/users");
				const allStaffs = response.data;

				if (searchTerm.trim()) {
					const filteredStaffs = allStaffs.filter((staff: staffUser) =>
						staff.fullname.toLowerCase().includes(searchTerm.toLowerCase()),
					);
					setStaffs(filteredStaffs);
				} else {
					setStaffs(allStaffs);
				}
			} catch (error) {
				console.error("Erro ao buscar funcionários:", error);
			}
		}
		getStaff();
	}, [searchTerm]);
	return (
		<>
			<div className="flex flex-col min-h-screen">
				<Header pagename={"Lista de Funcionários"} href={"/"} $logout={false} />

				<main className="flex-grow text-center flex flex-col md:text-lg">
					<Link
						to={"/novo-funcionario"}
						type="button"
						className="flex items-center gap-2.5 mx-auto mt-6 font-bold text-lg"
					>
						Adicionar Funcionário <FiPlus className="text-3xl" />
					</Link>

					<input
						type="text"
						placeholder="Buscar por nome do funcionário..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="border border-gray-300 rounded-md h-10 my-6 w-2/3 px-2 text-lg mx-auto"
					/>

					<div
						id="ClientListRender"
						className="px-2 grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 mb-2"
					>
						{staffs.length > 0 ? (
							staffs.map((staff) => (
								<div
									key={staff.user_id}
									className="h-full flex flex-col justify-center"
								>
									<div
										id="staffItem"
										className="border relative border-gray-300 rounded-md w-72 mx-auto flex flex-col text-left p-2 mb-2 h-fit"
									>
										{staff.user_id === user?.id ? (
											<></>
										) : (
											<IoClose
												className="text-red-600 absolute top-2 right-3 cursor-pointer text-2xl"
												onClick={() => {
													handleDeleteUser(staff.user_id);
												}}
											/>
										)}

										<h3 className="text-center text-lg truncate">
											{staff.fullname}
										</h3>
										<span className="truncate">
											<strong>CPF/CNPJ:</strong> {staff.cpf || staff.cnpj}
										</span>
										<span className="truncate">
											<strong>Contato:</strong> {staff.contact}
										</span>
										<span className="flex justify-between">
											<p className="truncate">
												<strong>Permissão:</strong>{" "}
												{staff.userType === "staff"
													? "Funcionário"
													: "Administrador"}
											</p>
										</span>
										{staff.userType === "staff" && (
											<span className="flex justify-between">
												<p className="truncate">
													<strong>Comissão:</strong>{" "}
													{new Intl.NumberFormat("en-US", {
														minimumFractionDigits: 0,
														maximumFractionDigits: 2,
													}).format(staff.comissionRate)}
													%
												</p>
											</span>
										)}
										<span className="w-full text-center">
											<Link
												type="button"
												className="border border-gray-300 rounded bg bg-blue-200 text-sm px-6 py-1 mt-2"
												to={`/atualizar-perfil/${staff.user_id}`}
											>
												Detalhes
											</Link>
										</span>
									</div>
								</div>
							))
						) : (
							<div className="col-span-full text-center text-xl text-gray-500 py-8">
								Nenhum funcionário encontrado com o nome "{searchTerm}"
							</div>
						)}
					</div>
				</main>

				<Footer />
			</div>
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => setIsAlertModalOpen(false)}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</>
	);
}
