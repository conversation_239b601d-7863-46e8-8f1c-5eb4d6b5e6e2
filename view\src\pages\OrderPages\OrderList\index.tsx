import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

import { api } from "../../../service/api";

interface Order {
	id: number;
	customer_id: number;
	staff_id: number;
	deadline: string;
	date: string;
	time: string;
	status: string;
	garments: Array<{
		id: number;
		quantity: number;
	}>;
}

interface Garment {
	id: number;
	name: string;
	refcode: string;
	price: string;
	size: string;
	color: string;
	in_stock: number;
}

interface staffUser {
	user_id: number;
	fullname: string;
	contact: string;
	cpf: string | null;
	cnpj: string | null;
	personType: "individual" | "legal";
	username: string;
	password: string;
	userType: "staff" | "admin";
	comissionRate: number;
}

interface ClientModel {
	id: number;
	fullname: string;
	contact: string;
	cpf: string | null;
	cnpj: string | null;
	personType: "individual" | "legal";
	address: string;
}

interface User {
	id: number;
	username: string;
	password: string;
	userType: "staff" | "admin";
	personData: {
		personId: number;
		fullname: string;
		contact: string;
		cpf: string;
		cnpj: string;
		personType: "individual" | "private";
	};
}

export function OrderList() {
	const [orders, setOrders] = useState<Order[]>([]);
	const [staff, setStaff] = useState<staffUser[]>([]);
	const [garments, setGarments] = useState<Garment[]>([]);
	const [custumer, setCustumer] = useState<ClientModel[]>([]);
	const [loading, setLoading] = useState(true);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
	const [actionType, setActionType] = useState<"finish" | "return" | "normal">(
		"normal",
	);
	const [stockChanges, setStockChanges] = useState<Map<number, number>>(
		new Map(),
	);
	const [alertModal, setAlertModal] = useState({
		isOpen: false,
		message: "",
		type: "success" as "success" | "error" | "confirm",
		onConfirm: () => { },
	});

	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		async function fetchData() {
			try {
				const [ordersRes, staffRes, garmentRes, custumerRes] =
					await Promise.all([
						api.get("/orders"),
						api.get("/users"),
						api.get("/garments"),
						api.get("/customers"),
					]);

				setOrders(ordersRes.data);
				setStaff(staffRes.data);
				setGarments(garmentRes.data);
				setCustumer(custumerRes.data);
			} catch (error) {
				console.error("Erro ao carregar dados:", error);
			} finally {
				setLoading(false);
			}
		}
		fetchData();
	}, [orders]);

	if (loading) {
		return <p>Carregando dados...</p>;
	}

	function getStaffDetailsById(staff_id: number) {
		if (staff.length === 0)
			return { fullname: "Carregando...", comissionRate: 0 };
		const staffMember = staff.find((s) => s.user_id === staff_id);
		return staffMember
			? {
				fullname: staffMember.fullname,
			}
			: { fullname: "Desconhecido", comissionRate: 0 };
	}

	function getGarmentDetailsById(garmentId: number) {
		return garments.find((g) => g.id === garmentId);
	}

	function getCustomerDetailsById(customer_id: number) {
		if (custumer.length === 0) return { fullname: "Carregando..." };
		const customer = custumer.find((c) => c.id === customer_id);
		return customer
			? { fullname: customer.fullname }
			: { fullname: "Desconhecido" };
	}

	function getStatusClass(status: string) {
		switch (status) {
			case "Fila de Espera":
				return "text-yellow-500 bg-yellow-50 border-yellow-500";
			case "Em Produção":
				return "text-blue-500 bg-blue-50 border-blue-500";
			case "Pronto pra Entrega":
				return "text-orange-500 bg-orange-50 border-orange-500";
			case "Concluído":
				return "text-green-500 bg-green-50 border-green-500";
			default:
				return "text-gray-500 bg-gray-50 border-gray-500";
		}
	}

	async function handleStatusChange(id: number, status: string) {
		try {
			if (status === "Fila de Espera") {
				await api.patch(`/orders/${id}`, { status: "Em Produção" });
			} else if (status === "Em Produção") {
				// Encontra o pedido atual
				const currentOrder = orders.find((order) => order.id === id);
				const newStockChanges = new Map<number, number>();

				// Atualiza o estoque de cada item do pedido apenas se não houver quantidade suficiente
				if (currentOrder) {
					for (const garment of currentOrder.garments) {
						const garmentDetails = getGarmentDetailsById(garment.id);
						if (garmentDetails && garmentDetails.in_stock < garment.quantity) {
							const quantityNeeded = garment.quantity - garmentDetails.in_stock;
							const newStock = garmentDetails.in_stock + quantityNeeded;

							// Registra a alteração de estoque
							newStockChanges.set(garment.id, quantityNeeded);

							await api.patch(`/garment/${garment.id}`, {
								in_stock: newStock,
							});
						}
					}
				}

				// Salva as alterações de estoque
				setStockChanges(newStockChanges);
				await api.patch(`/order/${id}`, { status: "Pronto pra Entrega" });
			} else if (status === "Pronto pra Entrega") {
				// Encontra o pedido atual
				const currentOrder = orders.find((order) => order.id === id);

				// Reverte as alterações de estoque
				if (currentOrder) {
					for (const garment of currentOrder.garments) {
						const garmentDetails = getGarmentDetailsById(garment.id);
						const stockChange = stockChanges.get(garment.id) || 0;

						if (garmentDetails && stockChange > 0) {
							const newStock = garmentDetails.in_stock - stockChange;
							await api.patch(`/garment/${garment.id}`, {
								in_stock: newStock,
							});
						}
					}
				}

				// Limpa o registro de alterações
				setStockChanges(new Map());
				await api.patch(`/orders/${id}`, { status: "Fila de Espera" });
			}

			// Atualiza a lista de pedidos e garments
			const [updatedOrdersRes, updatedGarmentsRes] = await Promise.all([
				api.get("/orders"),
				api.get("/garments"),
			]);

			setOrders(updatedOrdersRes.data);
			setGarments(updatedGarmentsRes.data);
		} catch (error) {
			console.error("Erro ao atualizar status:", error);
			setAlertModal({
				isOpen: true,
				message: "Erro ao atualizar o status do pedido",
				type: "error",
				onConfirm: () => setAlertModal({ ...alertModal, isOpen: false }),
			});
		}
	}

	async function handleFinishOrder(id: number) {
		try {
			// Encontra o pedido atual
			const currentOrder = orders.find((order) => order.id === id);

			// Subtrai do estoque a quantidade de cada item do pedido
			if (currentOrder) {
				for (const garment of currentOrder.garments) {
					const garmentDetails = getGarmentDetailsById(garment.id);
					if (garmentDetails) {
						const newStock = garmentDetails.in_stock - garment.quantity;
						await api.patch(`/garments/${garment.id}`, {
							in_stock: newStock,
						});
					}
				}
			}

			await api.patch(`/orders/${id}`, { status: "Concluído" });

			const [updatedOrdersRes, updatedGarmentsRes] = await Promise.all([
				api.get("/orders"),
				api.get("/garments"),
			]);

			setOrders(updatedOrdersRes.data);
			setGarments(updatedGarmentsRes.data);
		} catch (error) {
			console.error("Erro ao concluir pedido:", error);
			alert("Erro ao concluir o pedido");
		}
	}

	function getUserType() {
		const userType = user?.userType;

		if (userType === "admin") {
			return "border border-gray-300 rounded bg bg-red-200 py-2 px-2 flex";
		}

		return "hidden";
	}

	async function handleOrderDelete(id: number) {
		setAlertModal({
			isOpen: true,
			message: "Tem certeza que deseja excluir o pedido?",
			type: "confirm",
			onConfirm: async () => {
				try {
					await api.delete(`/orders/${id}`);
					setAlertModal({
						isOpen: true,
						message: "Seu pedido foi excluído...",
						type: "success",
						onConfirm: () => {
							setAlertModal({ ...alertModal, isOpen: false });
							setOrders([]);
						},
					});
				} catch (error) {
					console.error("Erro ao excluir pedido:", error);
					setAlertModal({
						isOpen: true,
						message: "Erro ao excluir o pedido",
						type: "error",
						onConfirm: () => setAlertModal({ ...alertModal, isOpen: false }),
					});
				}
			},
		});
	}

	// Função para abrir o modal com o tipo de ação correto
	const openModalWithAction = (
		order: Order,
		type: "finish" | "return" | "normal",
	) => {
		setSelectedOrder(order);
		setActionType(type);
		setIsModalOpen(true);
	};

	return (
		<>
			<div
				id="appContent"
				className="h-screen w-screen flex flex-col flex-grow"
			>
				<Header pagename={"Lista de Pedidos"} href={"/"} $logout={false} />

				<main
					className="flex-grow
	overflow-auto  font-roboto place-items-center
	grid gap-2 
	sm:grid-cols-2
	lg:grid-cols-3
	xl:grid-cols-4
	my-4
	"
				>
					{[...orders]
						.sort((a, b) => b.id - a.id)
						.map((order) => {
							const { fullname } = getStaffDetailsById(order.staff_id);

							const { fullname: customerName } = getCustomerDetailsById(
								order.customer_id,
							);
							const statusClass = getStatusClass(order.status);

							return (
								<div
									key={order.id}
									id="orderItem"
									className="flex flex-col gap-2 max-w-82 py-5 px-4 items-center border border-gray-300 rounded"
								>
									<h2 className="text-xl font-inter">Pedido {order.id}</h2>
									<div
										className={`text-lg border px-2 py-1 rounded ${statusClass}`}
									>
										Status: <span>{order.status}</span>
									</div>

									<div className="truncate">
										Cliente:
										<span className="text-stone-700"> {customerName}</span>
									</div>

									<div className="text-base truncate">
										Data e Hora:{" "}
										<span>
											{order.date} às {order.time}
										</span>
									</div>

									<div className="text-base truncate">
										Prazo: <span>{order.deadline}</span>
									</div>

									<div className="truncate">
										Vendedor:
										<span className="text-stone-700"> {fullname}</span>
									</div>

									{order.garments.map((garment) => {
										const garmentDetails = getGarmentDetailsById(garment.id);

										return (
											<div
												key={garment.id}
												className="productionItem border border-gray-300 rounded p-2 flex flex-col items-center "
											>
												<div className="flex gap-3">
													<div>
														Ref:&nbsp;
														<span className="text-stone-500">
															#{garmentDetails?.refcode}
														</span>
													</div>
													<div>
														Cor: <span>{garmentDetails?.color || "N/A"}</span>
													</div>
												</div>

												<div className="flex gap-3 whitespace-nowrap">
													<div>
														Tamanho:
														<span> {garmentDetails?.size || "N/A"}</span>
													</div>
													<div>
														Quantidade: <span> {garment.quantity}</span>
													</div>
												</div>

												{order.status !== "Concluído" && (
													<div
														className={`text-sm ${(garmentDetails?.in_stock || 0) >=
															garment.quantity
															? "text-green-600"
															: "text-red-600"
															}`}
													>
														Quantidade em estoque:{" "}
														{garmentDetails?.in_stock || 0}
													</div>
												)}
											</div>
										);
									})}

									<div className="flex flex-col gap-2">
										<div className="flex gap-2">
											{order.status === "Pronto pra Entrega" ? (
												<>
													<button
														type="button"
														className="border border-gray-300 rounded bg bg-yellow-200 py-2 px-2"
														onClick={() => openModalWithAction(order, "return")}
													>
														Voltar para Fila
													</button>
												</>
											) : (
												order.status !== "Concluído" && (
													<button
														type="button"
														className="border border-gray-300 rounded bg bg-blue-200 py-2 px-2"
														onClick={() => openModalWithAction(order, "normal")}
													>
														Alterar Status
													</button>
												)
											)}
											{order.status !== "Concluído" && (
												<Link
													to={`/atualizar-pedido/${order.id}`}
													className="border border-gray-300 rounded bg bg-blue-200 py-2 px-2 text-center"
												>
													Editar Pedido
												</Link>
											)}
										</div>

										<div className="flex flex-col items-center gap-2">
											{order.status === "Pronto pra Entrega" && (
												<button
													type="button"
													className="border border-gray-300 rounded bg bg-green-200 py-2 px-2"
													onClick={() => openModalWithAction(order, "finish")}
												>
													Concluir Pedido
												</button>
											)}
											<button
												type="button"
												className="border border-gray-300 rounded bg bg-red-200 py-2 px-2 "
												onClick={() => handleOrderDelete(order.id)}
											>
												Excluir Pedido
											</button>
										</div>
									</div>
								</div>
							);
						})}
				</main>

				<Footer />
			</div>
			{isModalOpen && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 font-roboto">
					<div className="bg-white p-8 rounded border border-gray-300 max-w-md w-full mx-4">
						<h3 className="text-xl font-inter mb-4 text-center">
							{actionType === "finish"
								? "Confirmar conclusão do pedido"
								: actionType === "return"
									? "Confirmar retorno à fila"
									: "Confirmar alteração de status"}
						</h3>

						<div className="mb-6 text-center">
							<p className="text-base">
								Status atual:{" "}
								<span className="font-medium">{selectedOrder?.status}</span>
							</p>
							<p className="text-base mt-2">
								{actionType === "finish" && (
									<span className="font-medium text-green-600">
										O pedido será marcado como concluído. Esta ação não poderá
										ser desfeita.
									</span>
								)}
								{actionType === "return" && (
									<span className="font-medium text-yellow-600">
										O pedido voltará para a Fila de Espera. Deseja continuar?
									</span>
								)}
								{actionType === "normal" && (
									<>
										Novo status:{" "}
										<span className="font-medium">
											{selectedOrder?.status === "Fila de Espera"
												? "Em Produção"
												: selectedOrder?.status === "Em Produção"
													? "Pronto pra Entrega"
													: ""}
										</span>
									</>
								)}
							</p>
						</div>

						<div className="flex justify-center gap-4">
							<button
								type="button"
								className="border border-gray-300 rounded bg-gray-200 py-2 px-6 hover:bg-gray-300 transition-colors"
								onClick={() => {
									setIsModalOpen(false);
									setActionType("normal");
								}}
							>
								Cancelar
							</button>
							<button
								type="button"
								className={`border border-gray-300 rounded py-2 px-6 transition-colors ${actionType === "finish"
									? "bg-green-200 hover:bg-green-300"
									: actionType === "return"
										? "bg-yellow-200 hover:bg-yellow-300"
										: "bg-blue-200 hover:bg-blue-300"
									}`}
								onClick={() => {
									if (actionType === "finish" && selectedOrder) {
										handleFinishOrder(selectedOrder.id);
									} else if (actionType === "return" && selectedOrder) {
										handleStatusChange(selectedOrder.id, selectedOrder.status);
									} else if (selectedOrder) {
										handleStatusChange(selectedOrder.id, selectedOrder.status);
									}
									setIsModalOpen(false);
									setActionType("normal");
								}}
							>
								Confirmar
							</button>
						</div>
					</div>
				</div>
			)}
			<AlertModal
				isOpen={alertModal.isOpen}
				message={alertModal.message}
				type={alertModal.type}
				onConfirm={alertModal.onConfirm}
				onClose={() => setAlertModal({ ...alertModal, isOpen: false })}
			/>
		</>
	);
}
