import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import MaskedInput from "react-text-mask";

import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

import { api } from "../../../service/api";

interface User {
	username: string;
	userType: "staff" | "admin";
	personId: number;
	fullname: string;
	contact: string;
	cpf: string | null;
	cnpj: string | null;
	personType: "individual" | "private";
	comissionRate: string;
}

const cpfMask = [
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	"-",
	/\d/,
	/\d/,
];

const cnpjMask = [
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	".",
	/\d/,
	/\d/,
	/\d/,
	"/",
	/\d/,
	/\d/,
	/\d/,
	/\d/,
	"-",
	/\d/,
	/\d/,
];

export function UpdateStaff() {
	const { id } = useParams();
	const [loading, setLoading] = useState(true);
	const [staff, setStaff] = useState<User | null>(null);

	const [isCpfSelected, setIsCpfSelected] = useState(true);

	const [staffIdentification, setStaffIdentification] = useState("");
	const [staffName, setStaffName] = useState("");
	const [staffUsername, setStaffUsername] = useState("");
	const [staffContact, setStaffContact] = useState("");
	const [staffComission, setStaffComission] = useState("");
	const [staffPermission, setStaffPermission] = useState("");

	const [alertMessage, setAlertMessage] = useState("");
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);
	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);

	const navigate = useNavigate();

	useEffect(() => {
		async function getStaff() {
			try {
				const response = await api.get(`/users/${id}`);
				const data = response.data;
				setStaff(data);
				setStaffName(data.fullname);
				setStaffUsername(data.username);
				setStaffContact(data.contact);
				setStaffPermission(data.userType);
				setStaffComission(data.comissionRate);

				// Ajustar CPF ou CNPJ e o estado isCpfSelected
				if (data.cpf) {
					setStaffIdentification(data.cpf);
					setIsCpfSelected(true);
				} else if (data.cnpj) {
					setStaffIdentification(data.cnpj);
					setIsCpfSelected(false);
				}
			} catch (error) {
				console.error("Erro ao buscar os dados do funcionário:", error);
			} finally {
				setLoading(false);
			}
		}

		getStaff();
	}, [id]);

	if (loading) {
		return (
			<div className="flex flex-col min-h-screen">
				<Header pagename="Atualizar Funcionário" href="/" $logout={false} />
				<main className="flex-grow text-center flex items-center justify-center">
					<p>Carregando dados...</p>
				</main>
				<Footer />
			</div>
		);
	}

	function handleIdentificationTypeChange(isCpf: boolean) {
		setIsCpfSelected(isCpf);

		const currentValue = staffIdentification.replace(/[^\d]/g, "");
		if (isCpf) {
			setStaffIdentification(currentValue.slice(0, 11));
		} else {
			setStaffIdentification(currentValue.slice(0, 14));
		}
	}

	async function handleUserUpdate(e: React.FormEvent) {
		e.preventDefault();

		// Validações dos campos
		if (!staffName || staffName.trim() === "") {
			setAlertMessage("O nome do funcionário não pode estar vazio.");
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffUsername || staffUsername.trim() === "") {
			setAlertMessage("O nome de usuário não pode estar vazio.");
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffContact || staffContact.trim() === "") {
			setAlertMessage("O contato não pode estar vazio.");
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			return;
		}

		if (!staffIdentification || staffIdentification.trim() === "") {
			setAlertMessage(
				`O ${isCpfSelected ? "CPF" : "CNPJ"} não pode estar vazio.`,
			);
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			return;
		}

		if (
			staffPermission === "staff" &&
			(!staffComission || staffComission.trim() === "")
		) {
			setAlertMessage(
				"O percentual de comissão não pode estar vazio para funcionários.",
			);
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
			return;
		}

		const identificationField = isCpfSelected
			? { cpf: staffIdentification.padStart(11, "0"), cnpj: null }
			: { cnpj: staffIdentification.padStart(14, "0"), cpf: null };

		const updatedData: Partial<User> = {};
		if (staffUsername !== staff?.username) updatedData.username = staffUsername;

		if (staffPermission === "staff" || staffPermission === "admin") {
			updatedData.userType = staffPermission;
		}

		if (staffName !== staff?.fullname) updatedData.fullname = staffName;
		if (staffContact !== staff?.contact) updatedData.contact = staffContact;
		if (staffComission !== staff?.comissionRate)
			updatedData.comissionRate = staffComission || "0.00";

		if (isCpfSelected && staffIdentification !== staff?.cpf) {
			updatedData.cpf = staffIdentification;
			updatedData.cnpj = null;
		} else if (!isCpfSelected && staffIdentification !== staff?.cnpj) {
			updatedData.cnpj = staffIdentification;
			updatedData.cpf = null;
		}

		const personType = staff?.cpf ? "individual" : "private";
		if (updatedData.cpf || updatedData.cnpj) {
			updatedData.personType = personType;
		}

		try {
			await api.patch(`/users/${id}`, updatedData);

			const handleSuccess = () => {
				navigate("/lista-de-funcionarios");
			};

			setAlertType("success");
			setAlertMessage("Funcionário atualizado com sucesso!");
			setAlertCallback(() => handleSuccess);
			setIsAlertModalOpen(true);
		} catch (error) {
			setAlertMessage("Não foi possível atualizar o funcionário.");
			setAlertType("error");
			setAlertCallback(null);
			setIsAlertModalOpen(true);
		}
	}

	return (
		<div className="flex flex-col min-h-screen">
			<Header pagename={"Atualizar Funcionário"} href={"/"} $logout={false} />

			<main className="flex-grow text-center flex flex-col md:text-lg">
				<main className="flex-grow flex flex-col items-center py-8 px-4">
					<form
						id="cad-funcionario"
						className="text-base"
						onSubmit={handleUserUpdate}
					>
						<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4 w-md">
							<div className="input-wrapper flex flex-col">
								<label
									htmlFor="nomefuncionario-input"
									className="text-left mb-1"
								>
									Nome do Funcionário:
								</label>
								<input
									type="text"
									id="nomefuncionario-input"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									value={staffName}
									onChange={(e) => setStaffName(e.target.value)}
								/>
							</div>

							<div className="flex flex-col">
								{/* Seleção entre CPF e CNPJ */}
								<div className="input-wrapper flex items-center space-x-4 mb-1.5">
									<div className="check flex items-center gap-1">
										<input
											type="radio"
											name="identification"
											id="cpfcheck"
											checked={isCpfSelected}
											onChange={() => handleIdentificationTypeChange(true)}
											className="form-radio w-5 h-5"
										/>
										<label htmlFor="cpfcheck" className="text-lg">
											CPF:
										</label>
									</div>
									<div className="check flex items-center gap-1">
										<input
											type="radio"
											name="identification"
											id="cnpjcheck"
											checked={!isCpfSelected}
											onChange={() => handleIdentificationTypeChange(false)}
											className="form-radio w-5 h-5"
										/>
										<label htmlFor="cnpjcheck" className="text-lg">
											CNPJ:
										</label>
									</div>
								</div>

								{isCpfSelected ? (
									<div className="input-wrapper">
										<label htmlFor="cpfnum" className="block text-left text-lg">
											CPF:
										</label>
										<MaskedInput
											mask={cpfMask}
											id="cpfnum"
											placeholder="___.___.___-__"
											className="w-full text-sm border border-gray-300 rounded px-2 py-1"
											value={staffIdentification}
											onChange={(e) => setStaffIdentification(e.target.value)}
										/>
									</div>
								) : (
									<div className="input-wrapper">
										<label
											htmlFor="cnpjnum"
											className="block text-left text-lg"
										>
											CNPJ:
										</label>
										<MaskedInput
											mask={cnpjMask}
											id="cnpjnum"
											placeholder="__.___.___/____-__"
											className="w-full text-sm border border-gray-300 rounded px-2 py-1"
											value={staffIdentification}
											onChange={(e) => setStaffIdentification(e.target.value)}
										/>
									</div>
								)}
							</div>

							<div className="input-wrapper flex flex-col">
								<label htmlFor="username-input" className="text-left mb-1">
									Nome de usuário: (Será usado para fazer login)
								</label>
								<input
									type="text"
									id="username-input"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									value={staffUsername}
									onChange={(e) => setStaffUsername(e.target.value)}
								/>
							</div>

							<div className="input-wrapper flex flex-col">
								<label htmlFor="email-input" className="text-left mb-1">
									Contato (Número ou E-mail):
								</label>
								<input
									type="text"
									id="email-input"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									value={staffContact}
									onChange={(e) => setStaffContact(e.target.value)}
								/>
							</div>

							{staffPermission === "staff" ? (
								<div className="line-wrapper flex items-center justify-between gap-4">
									<label htmlFor="percentual-input" className="text-left mb-1">
										<span>Percentual de comissão:</span>
									</label>
									<div className="input-wrapper">
										<input
											type="number"
											id="percentual-input"
											className="text-sm border border-gray-300 rounded px-2 py-1"
											value={staffComission}
											onChange={(e) => setStaffComission(e.target.value)}
										/>
										%
									</div>
								</div>
							) : (
								<></>
							)}

							<div className="input-wrapper flex flex-col">
								<label htmlFor="permission-input" className="text-left mb-1">
									Permissão:
								</label>
								<select
									name="select"
									id="permission-input"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									value={staffPermission}
									onChange={(e) => setStaffPermission(e.target.value)}
								>
									<option value="staff">Staff</option>
									<option value="admin">Administrador</option>
								</select>
							</div>

							<button
								type="submit"
								className="bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
							>
								Confirmar atualização
							</button>
						</fieldset>
					</form>
				</main>
			</main>

			<Footer />
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => {
					setIsAlertModalOpen(false);
					setAlertCallback(null);
				}}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback}
			/>
		</div>
	);
}
