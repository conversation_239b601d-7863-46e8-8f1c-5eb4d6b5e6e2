"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_routes_1 = __importDefault(require("./user_routes"));
const customer_routes_1 = __importDefault(require("./customer_routes"));
const order_routes_1 = __importDefault(require("./order_routes"));
const garment_routes_1 = __importDefault(require("./garment_routes"));
const routes = (0, express_1.Router)();
routes.get('/', (req, res) => {
    res.json({ message: "Olá! Estou funcionando com TypeScript!" });
});
routes.use('/users', user_routes_1.default);
routes.use('/customers', customer_routes_1.default);
routes.use('/orders', order_routes_1.default);
routes.use('/garments', garment_routes_1.default);
exports.default = routes;
